# 视觉脚本系统节点注册分类统计情况

**文档生成日期：** 2025年7月11日  
**分析范围：** 引擎底层、编辑器界面、服务器端功能节点

## 概述

本文档详细分析了视觉脚本系统中所有节点的注册和集成情况，按照引擎注册和编辑器集成两个维度进行分类统计。

---

## 第一段：既在引擎中注册又在编辑器中集成

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
| 001 | core/events/onStart | 开始 | ✓ | ✓ |
| 002 | core/events/onUpdate | 更新 | ✓ | ✓ |
| 003 | core/debug/print | 打印日志 | ✓ | ✓ |
| 004 | core/flow/delay | 延时 | ✓ | ✓ |
| 005 | math/basic/add | 加法 | ✓ | ✓ |

---

## 第二段：仅在引擎中注册，未在编辑器中集成

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
| 006 | core/flow/branch | 分支 | ✓ | ✗ |
| 007 | core/flow/sequence | 序列 | ✓ | ✗ |
| 008 | math/basic/subtract | 减法 | ✓ | ✗ |
| 009 | math/basic/multiply | 乘法 | ✓ | ✗ |
| 010 | math/basic/divide | 除法 | ✓ | ✗ |
| 011 | math/basic/modulo | 取模 | ✓ | ✗ |
| 012 | math/advanced/power | 幂运算 | ✓ | ✗ |
| 013 | math/advanced/sqrt | 平方根 | ✓ | ✗ |
| 014 | math/trigonometric/sin | 正弦 | ✓ | ✗ |
| 015 | math/trigonometric/cos | 余弦 | ✓ | ✗ |
| 016 | math/trigonometric/tan | 正切 | ✓ | ✗ |
| 017 | logic/flow/branch | 逻辑分支 | ✓ | ✗ |
| 018 | logic/comparison/equal | 相等 | ✓ | ✗ |
| 019 | logic/comparison/notEqual | 不相等 | ✓ | ✗ |
| 020 | logic/comparison/greater | 大于 | ✓ | ✗ |
| 021 | logic/comparison/greaterEqual | 大于等于 | ✓ | ✗ |
| 022 | logic/comparison/less | 小于 | ✓ | ✗ |
| 023 | logic/comparison/lessEqual | 小于等于 | ✓ | ✗ |
| 024 | logic/operation/and | 与 | ✓ | ✗ |
| 025 | logic/operation/or | 或 | ✓ | ✗ |
| 026 | logic/operation/not | 非 | ✓ | ✗ |
| 027 | logic/flow/toggle | 开关 | ✓ | ✗ |
| 028 | entity/get | 获取实体 | ✓ | ✗ |
| 029 | entity/component/get | 获取组件 | ✓ | ✗ |
| 030 | physics/raycast | 射线检测 | ✓ | ✗ |
| 031 | physics/applyForce | 应用力 | ✓ | ✗ |
| 032 | physics/softbody/createCloth | 创建布料 | ✓ | ✗ |
| 033 | physics/softbody/createRope | 创建绳索 | ✓ | ✗ |
| 034 | network/connectToServer | 连接到服务器 | ✓ | ✗ |
| 035 | network/sendMessage | 发送网络消息 | ✓ | ✗ |
| 036 | network/events/onMessage | 接收网络消息 | ✓ | ✗ |
| 037 | ai/animation/generateBodyAnimation | 生成身体动画 | ✓ | ✗ |
| 038 | ai/animation/generateFacialAnimation | 生成面部动画 | ✓ | ✗ |
| 039 | ai/nlp/classifyText | 文本分类 | ✓ | ✗ |
| 040 | ai/nlp/recognizeEntities | 命名实体识别 | ✓ | ✗ |
| 041 | debug/breakpoint | 断点 | ✓ | ✗ |
| 042 | debug/log | 日志 | ✓ | ✗ |
| 043 | debug/performanceTimer | 性能计时 | ✓ | ✗ |
| 044 | debug/variableWatch | 变量监视 | ✓ | ✗ |
| 045 | debug/assert | 断言 | ✓ | ✗ |
| 046 | GetTime | 获取时间 | ✓ | ✗ |
| 047 | Delay | 延迟（时间节点） | ✓ | ✗ |
| 048 | Timer | 计时器 | ✓ | ✗ |
| 049 | PlayAnimation | 播放动画 | ✓ | ✗ |
| 050 | StopAnimation | 停止动画 | ✓ | ✗ |
| 051 | SetAnimationSpeed | 设置动画速度 | ✓ | ✗ |
| 052 | GetAnimationState | 获取动画状态 | ✓ | ✗ |
| 053 | KeyboardInput | 键盘输入 | ✓ | ✗ |
| 054 | MouseInput | 鼠标输入 | ✓ | ✗ |
| 055 | TouchInput | 触摸输入 | ✓ | ✗ |
| 056 | GamepadInput | 手柄输入 | ✓ | ✗ |
| 057 | PlayAudio | 播放音频 | ✓ | ✗ |
| 058 | StopAudio | 停止音频 | ✓ | ✗ |
| 059 | SetVolume | 设置音量 | ✓ | ✗ |
| 060 | AudioAnalyzer | 音频分析器 | ✓ | ✗ |
| 061 | Audio3D | 3D音频 | ✓ | ✗ |

---

## 第三段：仅在编辑器中集成，未在引擎中注册

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
| 062 | core/math/add | 加法（编辑器版本） | ✗ | ✓ |

**注：** 编辑器中的 `core/math/add` 与引擎中的 `math/basic/add` 功能相同，但路径不同。

---

## 第四段：未实现

| 序号 | 节点名 | 节点中文名 | 引擎中注册 | 编辑器中注册 |
|------|--------|------------|------------|--------------|
| 063 | ui/button/onClick | 按钮点击事件 | ✗ | ✗ |
| 064 | ui/input/onChange | 输入框变化事件 | ✗ | ✗ |
| 065 | ui/slider/onValueChange | 滑块值变化事件 | ✗ | ✗ |
| 066 | string/concat | 字符串连接 | ✗ | ✗ |
| 067 | string/split | 字符串分割 | ✗ | ✗ |
| 068 | array/push | 数组添加元素 | ✗ | ✗ |
| 069 | array/pop | 数组移除元素 | ✗ | ✗ |
| 070 | object/getProperty | 获取对象属性 | ✗ | ✗ |
| 071 | object/setProperty | 设置对象属性 | ✗ | ✗ |
| 072 | variable/get | 获取变量 | ✗ | ✗ |
| 073 | variable/set | 设置变量 | ✗ | ✗ |
| 074 | function/call | 调用函数 | ✗ | ✗ |
| 075 | function/return | 返回值 | ✗ | ✗ |

---

## 统计汇总

### 按注册状态分类
- **既在引擎中注册又在编辑器中集成：** 5个节点
- **仅在引擎中注册：** 56个节点
- **仅在编辑器中集成：** 1个节点
- **未实现：** 13个节点

### 按功能分类统计

#### 引擎中已注册的节点分类：
- **核心节点（Core）：** 5个
- **数学节点（Math）：** 9个
- **逻辑节点（Logic）：** 11个
- **实体节点（Entity）：** 2个
- **物理节点（Physics）：** 4个
- **网络节点（Network）：** 3个
- **AI节点（AI）：** 4个
- **调试节点（Debug）：** 5个
- **时间节点（Time）：** 3个
- **动画节点（Animation）：** 4个
- **输入节点（Input）：** 4个
- **音频节点（Audio）：** 5个

#### 编辑器中已集成的节点分类：
- **事件节点（Events）：** 2个
- **调试节点（Debug）：** 1个
- **数学节点（Math）：** 1个
- **流程节点（Flow）：** 1个

### 集成度分析
- **引擎注册率：** 61/75 = 81.3%
- **编辑器集成率：** 6/75 = 8.0%
- **完整集成率：** 5/75 = 6.7%

---

## 建议和改进方向

### 1. 优先集成建议
建议优先将以下引擎已注册但编辑器未集成的重要节点添加到编辑器中：
- **流程控制节点：** 分支（core/flow/branch）、序列（core/flow/sequence）
- **基础数学运算：** 减法、乘法、除法、取模等
- **逻辑比较节点：** 相等、大于、小于、大于等于、小于等于等
- **逻辑运算节点：** 与、或、非运算
- **实体操作节点：** 获取实体、获取组件

### 2. 功能完善建议
建议实现以下缺失的基础功能节点：
- **字符串操作节点：** 连接、分割、查找、替换等
- **数组操作节点：** 添加、删除、查找、排序等
- **对象操作节点：** 获取属性、设置属性等
- **变量操作节点：** 获取变量、设置变量等
- **UI交互节点：** 按钮点击、输入框变化、滑块值变化等

### 3. 架构优化建议
- **统一节点注册机制：** 统一引擎和编辑器的节点注册接口
- **建立节点自动同步：** 引擎注册的节点自动在编辑器中可用
- **完善节点分类系统：** 建立更细致的节点分类和标签体系
- **增强节点搜索功能：** 支持按功能、分类、标签等多维度搜索
- **节点版本管理：** 建立节点版本控制和兼容性管理机制

### 4. 开发优先级建议
**高优先级（立即实现）：**
- 基础数学运算节点集成
- 逻辑比较和运算节点集成
- 流程控制节点集成

**中优先级（近期实现）：**
- 实体和组件操作节点集成
- 时间相关节点集成
- 调试节点集成

**低优先级（长期规划）：**
- AI相关节点集成
- 高级物理节点集成
- 网络相关节点集成

---

**文档结束**
