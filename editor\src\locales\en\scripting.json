{"scriptEditor": {"title": "Script Editor", "codeEditor": "Code Editor", "visualScript": "Visual Script", "visualEditor": "Visual Editor", "visualEditorDescription": "Create script logic using nodes and connections", "openVisualEditor": "Open Visual Editor", "settings": "Settings", "debug": "Debug", "debugMode": "Debug Mode", "debugFeatureInDevelopment": "Debug feature in development", "execute": "Execute", "stop": "Stop", "save": "Save", "executeScript": "Execute Script", "stopScript": "Stop Script", "saveScript": "<PERSON>", "enableScript": "Enable <PERSON>", "autoRun": "Auto Run", "scriptDomain": "Script Domain", "defaultDomain": "Default Domain", "uiDomain": "UI Domain", "physicsDomain": "Physics Domain", "audioDomain": "Audio Domain", "scriptSettings": "<PERSON><PERSON><PERSON> Settings", "contentEmpty": "Script content is empty", "executeSuccess": "<PERSON><PERSON><PERSON> executed successfully", "executeFailed": "<PERSON><PERSON><PERSON> execution failed", "saveSuccess": "<PERSON><PERSON><PERSON> saved successfully", "stopped": "<PERSON><PERSON><PERSON> stopped", "featureInDevelopment": "Feature in development", "codePlaceholder": "Write your script code here...", "template": "Template", "selectTemplate": "Select Template", "status": {"stopped": "Stopped", "running": "Running", "paused": "Paused", "error": "Error"}}, "codeEditor": {"placeholder": "Write your code here...", "copyCode": "Copy Code", "downloadCode": "Download Code", "uploadCode": "Upload Code", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "copySuccess": "Code copied to clipboard", "copyFailed": "Co<PERSON> failed", "downloadSuccess": "Code downloaded", "uploadSuccess": "Code uploaded"}, "visualScript": {"execute": "Execute", "stop": "Stop", "save": "Save", "addNode": "Add Node", "settings": "Settings", "fullscreen": "Fullscreen", "selectNode": "Select Node", "executeScript": "Execute Script", "stopScript": "Stop Script", "saveScript": "<PERSON>", "executeSuccess": "<PERSON><PERSON><PERSON> executed successfully", "executeFailed": "<PERSON><PERSON><PERSON> execution failed", "saveSuccess": "<PERSON><PERSON><PERSON> saved successfully", "stopped": "<PERSON><PERSON><PERSON> stopped", "nodeAdded": "Node added", "favorited": "Node favorited", "unfavorited": "Node unfavorited", "emptyCanvasTitle": "Start creating your visual script", "emptyCanvasDescription": "Click the button below to add your first node", "addFirstNode": "Add First Node"}, "nodes": {"startEvent": "Start Event", "startEventDescription": "Triggered when script starts", "updateEvent": "Update Event", "updateEventDescription": "Triggered every frame", "print": "Print", "printDescription": "Output information to console", "add": "Add", "addDescription": "Calculate sum of two numbers", "delay": "Delay", "delayDescription": "Continue execution after specified delay"}, "nodeSearch": {"searchPlaceholder": "Search nodes...", "all": "All", "favorites": "Favorites", "recent": "Recent", "category": "Category", "commonTags": "Common Tags", "selectedTags": "Selected Tags", "clearTags": "Clear Tags", "moreTags": "More Tags", "noResults": "No matching nodes found", "favorite": "Favorite", "unfavorite": "Unfavorite"}, "nodeCategories": {"events": "Events", "flow": "Flow Control", "logic": "Logic", "math": "Math", "string": "String", "debug": "Debug", "entity": "Entity", "transform": "Transform", "physics": "Physics", "animation": "Animation", "audio": "Audio", "input": "Input", "ui": "User Interface", "network": "Network", "custom": "Custom"}, "common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "import": "Import", "export": "Export", "upload": "Upload", "download": "Download", "preview": "Preview", "settings": "Settings", "help": "Help", "about": "About"}}